Metadata-Version: 2.0
Name: playsound
Version: 1.2.2
Summary: Pure Python, cross platform, single function module with no dependencies for playing sounds.
Home-page: https://github.com/TaylorSMarks/playsound
Author: <PERSON>
Author-email: <EMAIL>
License: MIT
Keywords: sound playsound music wave wav mp3 media song play audio
Platform: UNKNOWN
Classifier: Development Status :: 5 - Production/Stable
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: MIT License
Classifier: Operating System :: OS Independent
Classifier: Programming Language :: Python :: 2
Classifier: Programming Language :: Python :: 2.3
Classifier: Programming Language :: Python :: 2.4
Classifier: Programming Language :: Python :: 2.5
Classifier: Programming Language :: Python :: 2.6
Classifier: Programming Language :: Python :: 2.7
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.1
Classifier: Programming Language :: Python :: 3.2
Classifier: Programming Language :: Python :: 3.3
Classifier: Programming Language :: Python :: 3.4
Classifier: Programming Language :: Python :: 3.5
Classifier: Topic :: Multimedia :: Sound/Audio :: MIDI
Classifier: Topic :: Multimedia :: Sound/Audio :: Players
Classifier: Topic :: Multimedia :: Sound/Audio :: Players :: MP3

playsound
=========
*Pure Python, cross platform, single function module with no dependencies for playing sounds.*

Installation
------------
Install via pip:

.. code-block:: bash

    $ pip install playsound

Done.

If you insist on the (slightly) harder way of installing, from source,
you know how to do it already and don't need my help.

The latest version of the source code can be found at:
https://github.com/TaylorSMarks/playsound

Quick Start
-----------
Once you've installed, you can really quickly verified that it works with just this:

.. code-block:: python

    >>> from playsound import playsound
    >>> playsound('/path/to/a/sound/file/you/want/to/play.mp3') 

Documentation
-------------
The playsound module contains only one thing - the function (also named) playsound.

It requires one argument - the path to the file with the sound you'd like to play. This may be a local file, or a URL.

There's an optional second argument, block, which is set to True by default. Setting it to False makes the function run asynchronously.

On Windows, uses windll.winmm. WAVE and MP3 have been tested and are known to work. Other file formats may work as well.

On OS X, uses AppKit.NSSound. WAVE and MP3 have been tested and are known to work. In general, anything QuickTime can play, playsound should be able to play, for OS X.

On Linux, uses GStreamer. Known to work on Ubuntu 14.04 and ElementaryOS
Loki. Support for the ``block`` argument is currently not implemented.

Requirements
------------
I've only tested playsound it with Python 2.7 on Windows 7 and OS X 10.11, but
I expect that it should work on Windows XP thru 10 at least, OS X 10.5 and newer,
all versions of Linux, and Python 2.3 and newer.

Copyright
---------
This software is Copyright (c) 2016 Taylor Marks <<EMAIL>>.

See the bundled LICENSE file for more information.


