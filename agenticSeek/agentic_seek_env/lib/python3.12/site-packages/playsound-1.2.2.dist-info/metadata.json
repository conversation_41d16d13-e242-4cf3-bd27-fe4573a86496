{"classifiers": ["Development Status :: 5 - Production/Stable", "Intended Audience :: <PERSON><PERSON><PERSON>", "License :: OSI Approved :: MIT License", "Operating System :: OS Independent", "Programming Language :: Python :: 2", "Programming Language :: Python :: 2.3", "Programming Language :: Python :: 2.4", "Programming Language :: Python :: 2.5", "Programming Language :: Python :: 2.6", "Programming Language :: Python :: 2.7", "Programming Language :: Python :: 3", "Programming Language :: Python :: 3.1", "Programming Language :: Python :: 3.2", "Programming Language :: Python :: 3.3", "Programming Language :: Python :: 3.4", "Programming Language :: Python :: 3.5", "Topic :: Multimedia :: Sound/Audio :: MIDI", "Topic :: Multimedia :: Sound/Audio :: Players", "Topic :: Multimedia :: Sound/Audio :: Players :: MP3"], "extensions": {"python.details": {"contacts": [{"email": "<EMAIL>", "name": "<PERSON>", "role": "author"}], "document_names": {"description": "DESCRIPTION.rst"}, "project_urls": {"Home": "https://github.com/TaylorSMarks/playsound"}}}, "generator": "bdist_wheel (0.29.0)", "keywords": ["sound", "playsound", "music", "wave", "wav", "mp3", "media", "song", "play", "audio"], "license": "MIT", "metadata_version": "2.0", "name": "playsound", "summary": "Pure Python, cross platform, single function module with no dependencies for playing sounds.", "version": "1.2.2"}